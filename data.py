"""
Sample data for FPS Hub website
Enhanced with API integration and realistic data generation
"""
from models import Game, Character, Map, NewsArticle, Setting, Skill, ForumPost, EsportsTeam, Tournament, Player, ProPlayerSettings, CommunitySettings, Match
from datetime import datetime, timedelta

# Import enhanced models and data generation
try:
    from enhanced_models_simple import *
    from realistic_data_generator import RealisticDataGenerator
    ENHANCED_MODELS_AVAILABLE = True
    print("🚀 Enhanced models loaded successfully")
except ImportError as e:
    ENHANCED_MODELS_AVAILABLE = False
    print(f"ℹ️ Enhanced models not available: {e}")
    print("📊 Using basic data models")

# Games Data
GAMES = [
    Game(1, "Counter-Strike 2", "CS2", "The legendary tactical FPS game with precise gunplay and strategic team-based gameplay.", "Valve", 2023, "/static/images/games/cs2.jpg"),
    Game(2, "Valorant", "VAL", "A character-based tactical shooter with unique agent abilities and precise gunplay.", "Riot Games", 2020, "/static/images/games/valorant.jpg"),
    Game(3, "Apex Legends", "APEX", "A battle royale game with unique legends, each with special abilities and tactical gameplay.", "Respawn Entertainment", 2019, "/static/images/games/apex.jpg"),
    Game(4, "Call of Duty: Modern Warfare", "COD", "Fast-paced military shooter with various game modes and realistic combat.", "Infinity Ward", 2019, "/static/images/games/cod.jpg"),
    Game(5, "Overwatch 2", "OW2", "Team-based shooter with diverse heroes, each with unique abilities and roles.", "Blizzard Entertainment", 2022, "/static/images/games/overwatch.jpg"),
    Game(6, "Rainbow Six Siege", "R6", "Tactical shooter focused on environmental destruction and strategic team play.", "Ubisoft", 2015, "/static/images/games/r6.jpg")
]

# Characters Data
CHARACTERS = [
    # Valorant Agents
    Character(1, "Jett", "Duelist", ["Dash", "Updraft", "Cloudburst", "Blade Storm"], "Agile duelist from South Korea with wind-based abilities.", 2, "/static/images/characters/jett.jpg"),
    Character(2, "Sage", "Sentinel", ["Barrier Orb", "Slow Orb", "Healing Orb", "Resurrection"], "Support agent from China with healing and utility abilities.", 2, "/static/images/characters/sage.jpg"),
    Character(3, "Phoenix", "Duelist", ["Blaze", "Curveball", "Hot Hands", "Run It Back"], "Self-sufficient duelist from UK with fire-based abilities.", 2, "/static/images/characters/phoenix.jpg"),

    # Apex Legends
    Character(4, "Wraith", "Assault", ["Into the Void", "Voices from the Void", "Dimensional Rift"], "Mysterious legend with interdimensional abilities.", 3, "/static/images/characters/wraith.jpg"),
    Character(5, "Lifeline", "Support", ["Combat Medic", "D.O.C. Heal Drone", "Care Package"], "Medic legend focused on healing and support.", 3, "/static/images/characters/lifeline.jpg"),

    # Overwatch 2
    Character(6, "Tracer", "Damage", ["Blink", "Recall", "Pulse Bomb"], "Time-jumping adventurer with high mobility.", 5, "/static/images/characters/tracer.jpg"),
    Character(7, "Reinhardt", "Tank", ["Rocket Hammer", "Barrier Field", "Charge", "Earthshatter"], "Crusader tank with a massive hammer and shield.", 5, "/static/images/characters/reinhardt.jpg")
]

# Maps Data
MAPS = [
    # CS2 Maps
    Map(1, "Dust2", "Iconic desert map with long sightlines and strategic chokepoints.",
        ["Long A", "Short A", "B Tunnels", "Mid", "Catwalk"],
        ["Control mid for map control", "Use smokes for safe rotations", "Watch for AWP picks on long angles"],
        1, "/static/images/maps/dust2.jpg"),

    Map(2, "Mirage", "Balanced three-lane map perfect for tactical gameplay.",
        ["A Site", "B Site", "Mid", "Connector", "Palace"],
        ["Mid control is crucial", "Use one-way smokes", "Coordinate A site executes"],
        1, "/static/images/maps/mirage.jpg"),

    # Valorant Maps
    Map(3, "Bind", "Two-site map with teleporters connecting areas.",
        ["A Site", "B Site", "Hookah", "Lamps", "Teleporter"],
        ["Use teleporters for quick rotations", "Control Hookah for B site", "Watch for flanks through teleporter"],
        2, "/static/images/maps/bind.jpg")
]

# News Articles
NEWS = [
    # CS2 News Articles (inspired by HLTV.org content style)
    NewsArticle(1, "FaZe Clan defeats NAVI 2-1 in IEM Katowice 2024 Grand Final",
                "FaZe Clan claimed their second IEM Katowice title after a thrilling 2-1 victory over NAVI in the grand final. The series went to three maps with FaZe taking Mirage 16-12, losing Inferno 14-16, and closing out Ancient 16-10. s1mple put up impressive numbers with a 1.24 rating across the series, but it wasn't enough to prevent NAVI's defeat. karrigan's tactical prowess and rain's clutch performances in crucial rounds secured the $400,000 first-place prize for FaZe.",
                "HLTV Staff", datetime.now() - timedelta(hours=3), 1),

    NewsArticle(2, "Vitality announces ZywOo contract extension until 2027",
                "Team Vitality has secured the services of Mathieu 'ZywOo' Herbaut for three more years, extending his contract until 2027. The French superstar, who has been with Vitality since 2018, expressed his commitment to bringing more trophies to the organization. 'I'm excited to continue this journey with Vitality. We have unfinished business and I believe we can achieve great things together,' ZywOo stated in the announcement. The 24-year-old has been instrumental in Vitality's recent success, including their BLAST Premier Spring Final 2023 victory.",
                "Vitality Press Release", datetime.now() - timedelta(hours=8), 1),

    NewsArticle(3, "PGL Major Copenhagen 2024 format and teams revealed",
                "PGL has unveiled the format for the upcoming Major Championship in Copenhagen, featuring 24 teams competing for a $1,250,000 prize pool. The tournament will follow the traditional Swiss system format for the Challengers and Legends stages, with the top 8 teams advancing to single-elimination playoffs. Notable teams already qualified include FaZe Clan, NAVI, Vitality, and G2 Esports. The event is scheduled for March 17-31, 2024, at the Royal Arena in Copenhagen. This marks the first Major held in Denmark since the ELEAGUE Major 2018.",
                "PGL Official", datetime.now() - timedelta(hours=12), 1),

    NewsArticle(4, "Cloud9 parts ways with sh1ro, Russian star becomes free agent",
                "Cloud9 has announced the departure of Dmitry 'sh1ro' Sokolov from their active CS2 roster. The 23-year-old AWPer, who joined Cloud9 in February 2021, helped the team secure multiple tournament victories including the PGL Major Antwerp 2022. 'We thank sh1ro for his dedication and professionalism during his time with Cloud9. He will always be part of our family,' the organization stated. sh1ro is now exploring options with several top-tier European teams reportedly interested in his services.",
                "Cloud9 Official", datetime.now() - timedelta(hours=18), 1),

    NewsArticle(5, "ESL Pro League Season 19 groups drawn, FaZe and NAVI in Group A",
                "The groups for ESL Pro League Season 19 have been determined, with Group A featuring powerhouses FaZe Clan and NAVI alongside Vitality and Astralis. Group B includes G2 Esports, Team Liquid, MOUZ, and Complexity. The tournament will take place in Malta from April 23 to May 12, 2024, with a $850,000 prize pool. All matches will be played in a best-of-three format, with the top three teams from each group advancing to the playoffs. This season introduces a new overtime format and updated map pool including the recently reworked Dust2.",
                "ESL Gaming", datetime.now() - timedelta(days=1), 1),

    NewsArticle(6, "BLAST Premier Spring Groups 2024 schedule announced",
                "BLAST has released the complete schedule for the Spring Groups 2024, featuring 12 teams competing across two groups from February 19-25. Group A will see FaZe Clan, Vitality, G2 Esports, Astralis, Team Liquid, and MOUZ battle for playoff spots. Group B features NAVI, Cloud9, NIP, Complexity, FURIA, and Heroic. The top four teams from each group will advance to the Spring Final in Lisbon. All matches will be streamed live with enhanced production featuring new camera angles and player perspectives.",
                "BLAST Official", datetime.now() - timedelta(days=1, hours=6), 1),

    NewsArticle(7, "Counter-Strike 2 receives major update: weapon balance changes and new features",
                "Valve has released a significant update for Counter-Strike 2, introducing weapon balance adjustments and new gameplay features. The AK-47 receives a slight damage reduction at long range, while the M4A4 gets improved accuracy when moving. The update also includes a new 'Premier Mode' for competitive play, featuring enhanced anti-cheat measures and improved server tick rates. Map changes include updated callouts for Mirage and performance optimizations across all Active Duty maps. The update is now live across all regions.",
                "Valve Corporation", datetime.now() - timedelta(days=2), 1),

    NewsArticle(8, "HLTV releases 2024 team rankings: FaZe Clan claims #1 spot",
                "HLTV has published their latest world rankings, with FaZe Clan securing the top position following their recent tournament victories. The rankings reflect team performance over the past three months, with NAVI dropping to #2 and Vitality climbing to #3. G2 Esports maintains their #4 position, while Team Liquid rounds out the top 5. The rankings consider factors including tournament results, opponent strength, and recent form. FaZe's consistent performance across multiple events, including their IEM Katowice victory, solidified their position at the summit.",
                "HLTV.org", datetime.now() - timedelta(days=2, hours=8), 1),

    NewsArticle(9, "IEM Cologne 2024 dates confirmed: July tournament returns to LANXESS Arena",
                "ESL has confirmed that IEM Cologne 2024 will take place from July 7-21 at the iconic LANXESS Arena in Cologne, Germany. The tournament will feature 24 teams competing for a $1,000,000 prize pool in front of 20,000 spectators. Early bird tickets go on sale February 15, with general admission starting at €45. The event will mark the return of the 'Cathedral of Counter-Strike' after last year's successful edition. Team invitations will be announced in the coming weeks, with qualification spots available through regional tournaments.",
                "ESL Gaming", datetime.now() - timedelta(days=3), 1),

    NewsArticle(10, "Astralis announces coaching staff changes ahead of new season",
                "Astralis has revealed significant changes to their coaching structure, with Danny 'zonic' Sørensen stepping down as head coach to pursue new opportunities. The Danish organization has promoted assistant coach Alexander 'ave' Holdt to the head coach position. 'zonic has been instrumental in our success over the years, and we respect his decision to explore new challenges,' said Astralis Director of Sports Kasper Hvidt. The team will also welcome a new analyst and sports psychologist to support the roster's development.",
                "Astralis Official", datetime.now() - timedelta(days=3, hours=12), 1),

    # Valorant News Articles
    NewsArticle(11, "Valorant Champions Tour Updates", "New format changes coming to VCT 2024 include regional leagues and international events. Riot Games announces expanded prize pools and more opportunities for tier-2 teams.", "VCT News", datetime.now() - timedelta(days=4), 2),
    NewsArticle(12, "Sentinels dominates VCT Americas with TenZ leading the charge", "Sentinels secured their spot in VCT Masters after a dominant performance in the Americas league. TenZ's exceptional Jett plays and the team's improved coordination under new coach kaplan have been key factors in their success.", "VCT Americas", datetime.now() - timedelta(days=4, hours=8), 2),

    # Other Games News
    NewsArticle(13, "Apex Legends ALGS Championship", "Teams compete for the ultimate prize in the Apex Legends Global Series Championship. The final tournament features a $2M prize pool with the best teams from each region.", "ALGS Staff", datetime.now() - timedelta(days=5), 3),
    NewsArticle(14, "Overwatch 2 Season 9 Launch", "New hero, maps, and balance changes arrive in Overwatch 2's latest season. The meta is expected to shift significantly with these updates.", "Blizzard Entertainment", datetime.now() - timedelta(days=6), 5)
]

# Settings Data
SETTINGS = [
    # CS2 Settings
    Setting(1, "Graphics", "Resolution", "1920x1080", "Native resolution for best clarity", 1),
    Setting(2, "Graphics", "Display Mode", "Fullscreen", "Reduces input lag", 1),
    Setting(3, "Audio", "Master Volume", "0.5", "Balanced audio levels", 1),
    Setting(4, "Controls", "Mouse Sensitivity", "2.0", "Standard sensitivity for precision", 1),
    
    # Valorant Settings
    Setting(5, "Graphics", "Material Quality", "Medium", "Balance between performance and visuals", 2),
    Setting(6, "Audio", "Voice Chat Volume", "70", "Clear communication with team", 2)
]

# Skills Data
SKILLS = [
    Skill(1, "Crosshair Placement", "Keep crosshair at head level", "Beginner", 
          ["Always pre-aim common angles", "Adjust for different elevations", "Practice on aim maps"], 1),
    Skill(2, "Spray Control", "Master weapon recoil patterns", "Intermediate",
          ["Learn AK-47 and M4 patterns", "Practice on recoil maps", "Burst fire at long range"], 1),
    Skill(3, "Agent Abilities", "Maximize utility usage", "Beginner",
          ["Learn ability timings", "Coordinate with team", "Save abilities for key rounds"], 2)
]

# Forum Posts
FORUM_POSTS = [
    ForumPost(1, "Best CS2 Settings for Competitive", "What settings do you recommend for ranked play? I'm looking to optimize my config for better performance and visibility.", "ProGamer123", datetime.now() - timedelta(hours=2), 1),
    ForumPost(2, "Valorant Agent Tier List", "Current meta agents for each role. What do you think about the recent balance changes?", "ValorantPro", datetime.now() - timedelta(hours=5), 2),
    ForumPost(3, "Apex Legends Season 19 Discussion", "Thoughts on the new legend and map changes? The new abilities seem pretty balanced.", "ApexFan", datetime.now() - timedelta(hours=8), 3),
    ForumPost(4, "CS2 Smoke Lineups on Mirage", "Looking for some good smoke lineups for A site executes on Mirage. Any suggestions?", "SmokeKing", datetime.now() - timedelta(hours=12), 1),
    ForumPost(5, "Valorant Crosshair Settings", "Share your crosshair codes! I'm experimenting with different styles.", "AimGod", datetime.now() - timedelta(days=1), 2),
    ForumPost(6, "Overwatch 2 Tank Meta", "Discussion about the current tank meta and which heroes are strongest right now.", "TankMain", datetime.now() - timedelta(days=1), 5)
]

def get_game_by_id(game_id):
    return next((game for game in GAMES if game.id == game_id), None)

def get_characters_by_game(game_id):
    return [char for char in CHARACTERS if char.game_id == game_id]

def get_maps_by_game(game_id):
    return [map_obj for map_obj in MAPS if map_obj.game_id == game_id]

def get_news_by_game(game_id):
    return [news for news in NEWS if news.game_id == game_id]

def get_settings_by_game(game_id):
    return [setting for setting in SETTINGS if setting.game_id == game_id]

def get_skills_by_game(game_id):
    return [skill for skill in SKILLS if skill.game_id == game_id]

# Match Data
MATCHES = [
    # CS2 Matches - Recent Tournament Results
    Match(1, 1, 2, "FaZe Clan", "NAVI", 16, 14, datetime.now() - timedelta(hours=6), "IEM Katowice 2024 Grand Final", "Ancient"),
    Match(2, 1, 2, "FaZe Clan", "NAVI", 14, 16, datetime.now() - timedelta(hours=7), "IEM Katowice 2024 Grand Final", "Inferno"),
    Match(3, 1, 2, "FaZe Clan", "NAVI", 16, 12, datetime.now() - timedelta(hours=8), "IEM Katowice 2024 Grand Final", "Mirage"),
    Match(4, 11, 3, "Vitality", "G2 Esports", 16, 13, datetime.now() - timedelta(days=1), "BLAST Premier Spring Groups", "Dust2"),
    Match(5, 1, 5, "FaZe Clan", "Astralis", 16, 8, datetime.now() - timedelta(days=1, hours=6), "IEM Katowice 2024 Semi-Final", "Mirage"),
    Match(6, 2, 11, "NAVI", "Vitality", 16, 14, datetime.now() - timedelta(days=1, hours=12), "IEM Katowice 2024 Semi-Final", "Inferno"),
    Match(7, 3, 4, "G2 Esports", "Team Liquid", 16, 11, datetime.now() - timedelta(days=2), "ESL Pro League Season 19", "Overpass"),
    Match(8, 12, 13, "Cloud9", "MOUZ", 16, 19, datetime.now() - timedelta(days=2, hours=8), "BLAST Premier Spring Groups", "Nuke"),
    Match(9, 11, 5, "Vitality", "Astralis", 16, 6, datetime.now() - timedelta(days=3), "BLAST Premier Spring Groups", "Ancient"),
    Match(10, 1, 4, "FaZe Clan", "Team Liquid", 16, 12, datetime.now() - timedelta(days=3, hours=6), "IEM Katowice 2024 Quarter-Final", "Dust2"),

    # Valorant Matches
    Match(11, 6, 7, "Sentinels", "Fnatic", 13, 11, datetime.now() - timedelta(days=3), "VCT Masters", "Bind"),
    Match(12, 8, 9, "LOUD", "Paper Rex", 13, 7, datetime.now() - timedelta(days=6), "VCT Champions", "Haven"),
    Match(13, 6, 10, "Sentinels", "NRG", 13, 9, datetime.now() - timedelta(days=8), "VCT Americas", "Ascent"),
]

# Esports Teams Data
ESPORTS_TEAMS = [
    # CS2 Teams with Complete Professional Rosters
    EsportsTeam(1, "FaZe Clan", 1, "Europe", "/static/images/teams/faze.jpg", 1,
                players=[1, 11, 12, 13, 14, 15], founded_year=2010, coach="karrigan",
                recent_matches=[1, 2, 3, 5, 10],
                stats={"matches_played": 89, "matches_won": 64, "win_rate": 71.9, "avg_rating": 1.15},
                achievements=[
                    "IEM Katowice 2024 Champion",
                    "PGL Major Antwerp 2022 Champion",
                    "IEM Cologne 2022 Champion",
                    "BLAST Premier Fall Final 2021 Champion"
                ]),

    EsportsTeam(2, "NAVI", 2, "Europe", "/static/images/teams/navi.jpg", 1,
                players=[6, 7, 8, 9, 10], founded_year=2009, coach="B1ad3",
                recent_matches=[1, 2, 3, 6],
                stats={"matches_played": 95, "matches_won": 68, "win_rate": 71.6, "avg_rating": 1.14},
                achievements=[
                    "PGL Major Stockholm 2021 Champion",
                    "IEM Cologne 2021 Champion",
                    "BLAST Premier World Final 2021 Champion",
                    "IEM Katowice 2020 Champion"
                ]),

    EsportsTeam(3, "G2 Esports", 3, "Europe", "/static/images/g2.jpg", 1,
                players=[5, 16, 17, 18, 19, 20], founded_year=2013, coach="XTQZZZ",
                recent_matches=[4, 7],
                stats={"matches_played": 78, "matches_won": 49, "win_rate": 62.8, "avg_rating": 1.11},
                achievements=[
                    "ESL Pro League Season 15 Champion",
                    "IEM Dallas 2022 Champion",
                    "BLAST Premier Spring Groups 2022 Winner"
                ]),

    EsportsTeam(4, "Team Liquid", 4, "North America", "/static/images/liquid.jpg", 1,
                players=[29, 30], founded_year=2000, coach="daps",
                recent_matches=[7, 10],
                stats={"matches_played": 71, "matches_won": 42, "win_rate": 59.2, "avg_rating": 1.09},
                achievements=[
                    "Intel Grand Slam Season 2 Winner",
                    "ESL One Cologne 2019 Champion",
                    "IEM Chicago 2019 Champion",
                    "ESL Pro League Season 9 Champion"
                ]),

    EsportsTeam(5, "Astralis", 5, "Europe", "/static/images/astralis.jpg", 1,
                players=[4, 25, 26, 27, 28], founded_year=2016, coach="ave",
                recent_matches=[5, 9],
                stats={"matches_played": 67, "matches_won": 38, "win_rate": 56.7, "avg_rating": 1.08},
                achievements=[
                    "4x Major Champion (2017-2019)",
                    "Intel Grand Slam Season 1 Winner",
                    "BLAST Premier Global Final 2020 Champion",
                    "IEM Katowice 2018 Champion"
                ]),

    # Valorant Teams
    EsportsTeam(6, "Sentinels", 1, "North America", "/static/images/sentinels.jpg", 2,
                players=[6], founded_year=2020, coach="kaplan",
                recent_matches=[5, 7],
                stats={"matches_played": 35, "matches_won": 28, "win_rate": 80.0, "avg_rating": 1.18},
                achievements=["VCT Masters Reykjavik 2021 Champion", "VCT Stage 1 Masters 2021"]),

    EsportsTeam(7, "Fnatic", 2, "Europe", "/static/images/fnatic.jpg", 2,
                players=[], founded_year=2004, coach="mini",
                recent_matches=[5],
                stats={"matches_played": 40, "matches_won": 30, "win_rate": 75.0, "avg_rating": 1.15},
                achievements=["VCT LOCK//IN São Paulo 2023 Champion", "VCT Masters Tokyo 2023 Champion"]),

    EsportsTeam(8, "LOUD", 3, "Brazil", "/static/images/loud.jpg", 2,
                players=[], founded_year=2019, coach="bzka",
                recent_matches=[6],
                stats={"matches_played": 38, "matches_won": 27, "win_rate": 71.1, "avg_rating": 1.13},
                achievements=["VCT Champions 2022 Champion", "VCT Masters Reykjavik 2022 Champion"]),

    EsportsTeam(9, "Paper Rex", 4, "Asia Pacific", "/static/images/prx.jpg", 2,
                players=[], founded_year=2020, coach="alecks",
                recent_matches=[6],
                stats={"matches_played": 32, "matches_won": 22, "win_rate": 68.8, "avg_rating": 1.10},
                achievements=["VCT Masters Copenhagen 2022 Runner-up", "VCT Champions 2022 3rd Place"]),

    EsportsTeam(10, "NRG", 5, "North America", "/static/images/nrg.jpg", 2,
                players=[], founded_year=2016, coach="Chet",
                recent_matches=[7],
                stats={"matches_played": 30, "matches_won": 18, "win_rate": 60.0, "avg_rating": 1.07},
                achievements=["VCT Americas League 2023 3rd Place", "VCT Masters Tokyo 2023 4th Place"]),

    # Additional CS2 Teams for better scrolling demonstration
    EsportsTeam(11, "Vitality", 6, "Europe", "/static/images/vitality.jpg", 1,
                players=[2, 21, 22, 23, 24], founded_year=2013, coach="XTQZZZ",
                recent_matches=[4, 6, 9],
                stats={"matches_played": 82, "matches_won": 56, "win_rate": 68.3, "avg_rating": 1.13},
                achievements=[
                    "BLAST Premier Spring Final 2023 Champion",
                    "IEM Cologne 2023 Runner-up",
                    "ESL Pro League Season 17 Champion"
                ]),

    EsportsTeam(12, "Cloud9", 7, "North America", "/static/images/cloud9.jpg", 1,
                players=[3], founded_year=2013, coach="Hooxi",
                recent_matches=[8],
                stats={"matches_played": 33, "matches_won": 17, "win_rate": 51.5, "avg_rating": 1.04},
                achievements=["ELEAGUE Major 2018 Champion", "ESL Pro League Season 4 Champion"]),

    EsportsTeam(13, "MOUZ", 8, "Europe", "/static/images/mouz.jpg", 1,
                players=[], founded_year=2002, coach="sycrone",
                recent_matches=[8],
                stats={"matches_played": 31, "matches_won": 15, "win_rate": 48.4, "avg_rating": 1.02},
                achievements=["ESL One New York 2023 Runner-up", "IEM Dallas 2023 Semi-finalist"]),

    # Additional Valorant Teams
    EsportsTeam(14, "DRX", 6, "Asia Pacific", "/static/images/drx.jpg", 2,
                players=[], founded_year=2020, coach="glow",
                recent_matches=[],
                stats={"matches_played": 28, "matches_won": 16, "win_rate": 57.1, "avg_rating": 1.05},
                achievements=["VCT Masters Copenhagen 2022 3rd Place", "VCT Champions 2022 4th Place"]),

    EsportsTeam(15, "100 Thieves", 7, "North America", "/static/images/100t.jpg", 2,
                players=[], founded_year=2017, coach="mikes",
                recent_matches=[],
                stats={"matches_played": 26, "matches_won": 14, "win_rate": 53.8, "avg_rating": 1.03},
                achievements=["VCT Stage 3 Challengers 1 Champion", "First Strike North America Champion"]),
]

# Comprehensive Tournament Database (All-Time Major CS2 Tournaments)
TOURNAMENTS = [
    # Major Championships
    Tournament(1, "PGL Major Stockholm 2021", datetime(2021, 10, 26), datetime(2021, 11, 7), "$2,000,000", "Stockholm, Sweden", "/static/images/pgl_stockholm.jpg", 1),
    Tournament(2, "PGL Major Antwerp 2022", datetime(2022, 5, 9), datetime(2022, 5, 22), "$1,000,000", "Antwerp, Belgium", "/static/images/pgl_antwerp.jpg", 1),
    Tournament(3, "IEM Rio Major 2022", datetime(2022, 10, 31), datetime(2022, 11, 13), "$1,250,000", "Rio de Janeiro, Brazil", "/static/images/iem_rio.jpg", 1),
    Tournament(4, "BLAST.tv Paris Major 2023", datetime(2023, 5, 8), datetime(2023, 5, 21), "$1,250,000", "Paris, France", "/static/images/blast_paris.jpg", 1),
    Tournament(5, "PGL Major Copenhagen 2024", datetime(2024, 3, 17), datetime(2024, 3, 31), "$1,250,000", "Copenhagen, Denmark", "/static/images/pgl_copenhagen.jpg", 1),

    # IEM Events
    Tournament(6, "IEM Katowice 2024", datetime(2024, 2, 3), datetime(2024, 2, 11), "$1,000,000", "Katowice, Poland", "/static/images/iem_katowice_2024.jpg", 1),
    Tournament(7, "IEM Cologne 2023", datetime(2023, 7, 26), datetime(2023, 8, 6), "$1,000,000", "Cologne, Germany", "/static/images/iem_cologne_2023.jpg", 1),
    Tournament(8, "IEM Dallas 2023", datetime(2023, 5, 29), datetime(2023, 6, 4), "$250,000", "Dallas, USA", "/static/images/iem_dallas_2023.jpg", 1),
    Tournament(9, "IEM Sydney 2023", datetime(2023, 5, 10), datetime(2023, 5, 14), "$250,000", "Sydney, Australia", "/static/images/iem_sydney_2023.jpg", 1),
    Tournament(10, "IEM Katowice 2020", datetime(2020, 2, 25), datetime(2020, 3, 1), "$500,000", "Katowice, Poland", "/static/images/iem_katowice_2020.jpg", 1),

    # BLAST Events
    Tournament(11, "BLAST Premier World Final 2023", datetime(2023, 12, 13), datetime(2023, 12, 17), "$1,000,000", "Abu Dhabi, UAE", "/static/images/blast_world_final_2023.jpg", 1),
    Tournament(12, "BLAST Premier Spring Final 2023", datetime(2023, 6, 14), datetime(2023, 6, 18), "$425,000", "Washington D.C., USA", "/static/images/blast_spring_2023.jpg", 1),
    Tournament(13, "BLAST Premier Fall Final 2023", datetime(2023, 11, 22), datetime(2023, 11, 26), "$425,000", "Copenhagen, Denmark", "/static/images/blast_fall_2023.jpg", 1),
    Tournament(14, "BLAST Premier Spring Groups 2024", datetime(2024, 1, 22), datetime(2024, 1, 28), "$425,000", "London, UK", "/static/images/blast_spring_2024.jpg", 1),

    # ESL Pro League
    Tournament(15, "ESL Pro League Season 18", datetime(2023, 8, 16), datetime(2023, 9, 10), "$850,000", "Malta", "/static/images/esl_s18.jpg", 1),
    Tournament(16, "ESL Pro League Season 17", datetime(2023, 3, 22), datetime(2023, 4, 16), "$850,000", "Malta", "/static/images/esl_s17.jpg", 1),
    Tournament(17, "ESL Pro League Season 19", datetime(2024, 4, 23), datetime(2024, 5, 12), "$850,000", "Malta", "/static/images/esl_s19.jpg", 1),

    # Historical Major Events
    Tournament(18, "ELEAGUE Major 2017", datetime(2017, 1, 22), datetime(2017, 1, 29), "$1,000,000", "Atlanta, USA", "/static/images/eleague_2017.jpg", 1),
    Tournament(19, "FACEIT Major 2018", datetime(2018, 9, 5), datetime(2018, 9, 23), "$1,000,000", "London, UK", "/static/images/faceit_2018.jpg", 1),
    Tournament(20, "StarLadder Major 2019", datetime(2019, 8, 28), datetime(2019, 9, 8), "$1,000,000", "Berlin, Germany", "/static/images/starladder_2019.jpg", 1),
    Tournament(21, "MLG Major Columbus 2016", datetime(2016, 3, 29), datetime(2016, 4, 3), "$1,000,000", "Columbus, USA", "/static/images/mlg_columbus.jpg", 1),
    Tournament(22, "ESL One Cologne 2016", datetime(2016, 7, 5), datetime(2016, 7, 10), "$1,000,000", "Cologne, Germany", "/static/images/esl_cologne_2016.jpg", 1),

    # Valorant Tournaments (keeping existing)
    Tournament(50, "VCT Masters Madrid", datetime(2024, 3, 14), datetime(2024, 3, 24), "$1,000,000", "Madrid, Spain", "/static/images/vct_madrid.jpg", 2),
    Tournament(51, "VCT Champions 2024", datetime(2024, 8, 1), datetime(2024, 8, 25), "$2,250,000", "Seoul, South Korea", "/static/images/vct_champions.jpg", 2),
    Tournament(52, "VCT Americas Stage 1", datetime(2024, 1, 29), datetime(2024, 3, 10), "$300,000", "Los Angeles, USA", "/static/images/vct_americas.jpg", 2),
]

# Comprehensive Players Database (50+ Professional CS2 Players)
PLAYERS = [
    # Tier 1 European Players
    Player(1, "s1mple", "Oleksandr Kostyliev", 1, "FaZe Clan", "Ukraine", "$1,912,000", 1,
           "Legendary Ukrainian AWPer, considered the GOAT of Counter-Strike",
           stats={"rating_2_1": 1.28, "kd_ratio": 1.31, "adr": 85.2, "kast": 75.8, "maps_played": 156},
           age=27, current_team_id=1,
           social_links={"twitter": "@s1mpleO", "twitch": "s1mple", "instagram": "s1mpleo"},
           team_history=[
               {"team": "FaZe Clan", "period": "2024 - Present", "achievements": ["IEM Katowice 2024"]},
               {"team": "NAVI", "period": "2016 - 2024", "achievements": ["PGL Major Stockholm 2021", "IEM Cologne 2021"]},
               {"team": "Team Liquid", "period": "2016", "achievements": ["ESL One New York 2016"]}
           ],
           major_achievements=[
               {"tournament": "PGL Major Stockholm 2021", "placement": "1st", "mvp": True, "prize": "$400,000"},
               {"tournament": "IEM Katowice 2024", "placement": "1st", "mvp": True, "prize": "$400,000"}
           ],
           recent_matches=[1, 2, 3, 5, 10]),

    Player(2, "ZywOo", "Mathieu Herbaut", 2, "Vitality", "France", "$1,200,000", 1,
           "French superstar with incredible aim and game sense",
           stats={"rating_2_1": 1.31, "kd_ratio": 1.35, "adr": 89.7, "kast": 76.8, "maps_played": 142},
           age=24, current_team_id=11,
           team_history=[
               {"team": "Vitality", "period": "2018 - Present", "achievements": ["BLAST Premier Spring Final 2023"]}
           ],
           recent_matches=[4, 6, 9]),

    Player(3, "sh1ro", "Dmitry Sokolov", 3, "Cloud9", "Russia", "$890,000", 1,
           "Consistent AWPer with excellent positioning",
           stats={"rating_2_1": 1.18, "kd_ratio": 1.28, "adr": 78.4, "kast": 72.1, "maps_played": 128},
           age=23, current_team_id=12,
           recent_matches=[8]),

    Player(4, "device", "Nicolai Reedtz", 4, "Astralis", "Denmark", "$1,850,000", 1,
           "Legendary Danish AWPer and former world #1",
           stats={"rating_2_1": 1.15, "kd_ratio": 1.22, "adr": 76.8, "kast": 74.2, "maps_played": 189},
           age=29, current_team_id=5,
           major_achievements=[
               {"tournament": "ELEAGUE Major 2017", "placement": "1st", "mvp": False, "prize": "$500,000"},
               {"tournament": "FACEIT Major 2018", "placement": "1st", "mvp": True, "prize": "$500,000"}
           ]),

    Player(5, "NiKo", "Nikola Kovač", 5, "G2 Esports", "Bosnia", "$1,100,000", 1,
           "Rifler with exceptional individual skill and leadership",
           stats={"rating_2_1": 1.19, "kd_ratio": 1.25, "adr": 81.3, "kast": 73.5, "maps_played": 167},
           age=27, current_team_id=3),

    Player(6, "electronic", "Denis Sharipov", 6, "NAVI", "Russia", "$950,000", 1,
           "Versatile rifler and support player",
           stats={"rating_2_1": 1.12, "kd_ratio": 1.18, "adr": 75.6, "kast": 72.8, "maps_played": 145},
           age=26, current_team_id=2),

    Player(7, "Perfecto", "Ilya Zalutskiy", 7, "NAVI", "Russia", "$650,000", 1,
           "Support player with excellent utility usage",
           stats={"rating_2_1": 1.05, "kd_ratio": 1.08, "adr": 68.9, "kast": 71.2, "maps_played": 134},
           age=25, current_team_id=2),

    Player(8, "b1t", "Valeriy Vakhovskiy", 8, "NAVI", "Ukraine", "$580,000", 1,
           "Young talent with great potential",
           stats={"rating_2_1": 1.14, "kd_ratio": 1.21, "adr": 77.4, "kast": 73.1, "maps_played": 98},
           age=22, current_team_id=2),

    Player(9, "Aleksib", "Aleksi Virolainen", 9, "NAVI", "Finland", "$720,000", 1,
           "In-game leader with tactical expertise",
           stats={"rating_2_1": 1.08, "kd_ratio": 1.12, "adr": 71.8, "kast": 70.5, "maps_played": 156},
           age=28, current_team_id=2),

    Player(10, "iM", "Mihai-Cosmin Ivan", 10, "NAVI", "Romania", "$450,000", 1,
           "Support player and team coordinator",
           stats={"rating_2_1": 1.02, "kd_ratio": 1.05, "adr": 66.2, "kast": 69.8, "maps_played": 87},
           age=24, current_team_id=2),

    # FaZe Clan Players
    Player(11, "karrigan", "Finn Andersen", 11, "FaZe Clan", "Denmark", "$1,200,000", 1,
           "Veteran IGL with championship experience",
           stats={"rating_2_1": 1.06, "kd_ratio": 1.09, "adr": 69.5, "kast": 70.2, "maps_played": 178},
           age=34, current_team_id=1),

    Player(12, "rain", "Håvard Nygaard", 12, "FaZe Clan", "Norway", "$980,000", 1,
           "Consistent rifler and clutch player",
           stats={"rating_2_1": 1.11, "kd_ratio": 1.16, "adr": 74.8, "kast": 72.6, "maps_played": 165},
           age=30, current_team_id=1),

    Player(13, "Twistzz", "Russel Van Dulken", 13, "FaZe Clan", "Canada", "$850,000", 1,
           "North American star with excellent mechanics",
           stats={"rating_2_1": 1.15, "kd_ratio": 1.20, "adr": 78.2, "kast": 74.1, "maps_played": 142},
           age=25, current_team_id=1),

    Player(14, "frozen", "David Čerňanský", 14, "FaZe Clan", "Slovakia", "$720,000", 1,
           "Young talent with great potential",
           stats={"rating_2_1": 1.13, "kd_ratio": 1.18, "adr": 76.4, "kast": 73.8, "maps_played": 128},
           age=23, current_team_id=1),

    Player(15, "ropz", "Robin Kool", 15, "FaZe Clan", "Estonia", "$890,000", 1,
           "Versatile player with high game IQ",
           stats={"rating_2_1": 1.17, "kd_ratio": 1.22, "adr": 79.1, "kast": 75.2, "maps_played": 156},
           age=25, current_team_id=1),

    # G2 Esports Players
    Player(16, "huNter-", "Nemanja Kovač", 16, "G2 Esports", "Bosnia", "$780,000", 1,
           "Rifler with excellent aim and positioning",
           stats={"rating_2_1": 1.14, "kd_ratio": 1.19, "adr": 77.6, "kast": 73.9, "maps_played": 134},
           age=29, current_team_id=3),

    Player(17, "m0NESY", "Ilya Osipov", 17, "G2 Esports", "Russia", "$650,000", 1,
           "Young AWPer with incredible potential",
           stats={"rating_2_1": 1.21, "kd_ratio": 1.26, "adr": 82.3, "kast": 74.7, "maps_played": 98},
           age=19, current_team_id=3),

    Player(18, "nexa", "Nemanja Isaković", 18, "G2 Esports", "Serbia", "$720,000", 1,
           "In-game leader and support player",
           stats={"rating_2_1": 1.07, "kd_ratio": 1.11, "adr": 70.8, "kast": 71.4, "maps_played": 145},
           age=27, current_team_id=3),

    Player(19, "JACKZ", "Audric Jug", 19, "G2 Esports", "France", "$580,000", 1,
           "Entry fragger with aggressive playstyle",
           stats={"rating_2_1": 1.09, "kd_ratio": 1.13, "adr": 73.2, "kast": 71.8, "maps_played": 156},
           age=28, current_team_id=3),

    Player(20, "Aleksi", "Aleksi Virolainen", 20, "G2 Esports", "Finland", "$690,000", 1,
           "Tactical mastermind and team coordinator",
           stats={"rating_2_1": 1.05, "kd_ratio": 1.08, "adr": 68.4, "kast": 70.1, "maps_played": 123},
           age=28, current_team_id=3),

    # Vitality Players
    Player(21, "apEX", "Dan Madesclaire", 21, "Vitality", "France", "$890,000", 1,
           "Veteran IGL with championship experience",
           stats={"rating_2_1": 1.08, "kd_ratio": 1.12, "adr": 72.1, "kast": 71.6, "maps_played": 167},
           age=31, current_team_id=11),

    Player(22, "dupreeh", "Peter Rasmussen", 22, "Vitality", "Denmark", "$1,100,000", 1,
           "Four-time Major champion",
           stats={"rating_2_1": 1.10, "kd_ratio": 1.15, "adr": 74.8, "kast": 72.9, "maps_played": 189},
           age=31, current_team_id=11),

    Player(23, "Magisk", "Emil Reif", 23, "Vitality", "Denmark", "$950,000", 1,
           "Versatile player with clutch gene",
           stats={"rating_2_1": 1.12, "kd_ratio": 1.17, "adr": 76.2, "kast": 73.4, "maps_played": 178},
           age=26, current_team_id=11),

    Player(24, "flameZ", "Shahar Shushan", 24, "Vitality", "Israel", "$620,000", 1,
           "Rising star with great mechanics",
           stats={"rating_2_1": 1.16, "kd_ratio": 1.21, "adr": 78.9, "kast": 74.6, "maps_played": 98},
           age=22, current_team_id=11),

    # Astralis Players
    Player(25, "gla1ve", "Lukas Rossander", 25, "Astralis", "Denmark", "$1,300,000", 1,
           "Legendary IGL and four-time Major champion",
           stats={"rating_2_1": 1.04, "kd_ratio": 1.07, "adr": 67.8, "kast": 69.9, "maps_played": 198},
           age=29, current_team_id=5),

    Player(26, "Xyp9x", "Andreas Højsleth", 26, "Astralis", "Denmark", "$1,150,000", 1,
           "Clutch king and four-time Major champion",
           stats={"rating_2_1": 1.06, "kd_ratio": 1.09, "adr": 69.4, "kast": 71.2, "maps_played": 201},
           age=29, current_team_id=5),

    Player(27, "k0nfig", "Kristian Wienecke", 27, "Astralis", "Denmark", "$780,000", 1,
           "Aggressive rifler with high impact",
           stats={"rating_2_1": 1.13, "kd_ratio": 1.18, "adr": 77.1, "kast": 73.2, "maps_played": 134},
           age=27, current_team_id=5),

    Player(28, "blameF", "Benjamin Bremer", 28, "Astralis", "Denmark", "$690,000", 1,
           "IGL and rifler with tactical knowledge",
           stats={"rating_2_1": 1.09, "kd_ratio": 1.13, "adr": 73.6, "kast": 72.1, "maps_played": 145},
           age=27, current_team_id=5),

    # Team Liquid Players
    Player(29, "YEKINDAR", "Mareks Gaļinskis", 29, "Team Liquid", "Latvia", "$750,000", 1,
           "Entry fragger with explosive playstyle",
           stats={"rating_2_1": 1.15, "kd_ratio": 1.20, "adr": 79.3, "kast": 74.8, "maps_played": 123},
           age=25, current_team_id=4),

    Player(30, "NAF", "Keith Markovic", 30, "Team Liquid", "Canada", "$890,000", 1,
           "Consistent rifler and clutch player",
           stats={"rating_2_1": 1.11, "kd_ratio": 1.16, "adr": 75.4, "kast": 73.1, "maps_played": 167},
           age=28, current_team_id=4),

    # Valorant Players (keeping existing)
    Player(50, "TenZ", "Tyson Ngo", 1, "Sentinels", "Canada", "$450,000", 2,
           "Popular content creator and skilled duelist known for his streaming career",
           stats={
               "rating_2_1": 1.24,
               "kd_ratio": 1.18,
               "adr": 156.7,
               "acs": 245.3,
               "first_kills": 0.18,
               "first_deaths": 0.14,
               "clutch_success": 0.32,
               "maps_played": 15,
               "matches_played": 28,
               "matches_won": 18,
               "win_rate": 64.3
           },
           age=23, current_team_id=6,
           social_links={
               "twitter": "https://twitter.com/TenZOfficial",
               "twitch": "https://twitch.tv/tenz",
               "instagram": "https://instagram.com/tenzofficial",
               "youtube": "https://youtube.com/c/TenZ"
           },
           team_history=[
               {"team": "Sentinels", "period": "Apr 2021 - Present", "team_id": 6, "achievements": ["VCT Masters Reykjavik 2021", "VCT Stage 1 Masters 2021"]},
               {"team": "Cloud9", "period": "Apr 2020 - Apr 2021", "team_id": None, "achievements": []}
           ],
           major_achievements=[
               {"tournament": "VCT Masters Reykjavik 2021", "placement": "1st", "mvp": True, "prize": "$200,000"},
               {"tournament": "VCT Stage 1 Masters 2021", "placement": "1st", "mvp": False, "prize": "$100,000"}
           ],
           recent_matches=[11, 13],
           photos=[
               "/static/images/players/tenz_1.jpg",
               "/static/images/players/tenz_2.jpg"
           ],
           external_stats={
               "tracker_rating": 1.24,
               "vlr_rating": 1.18,
               "radiant_rank": True
           })
]

def get_forum_posts_by_game(game_id):
    return [post for post in FORUM_POSTS if post.game_id == game_id]

def get_teams_by_game(game_id):
    return [team for team in ESPORTS_TEAMS if team.game_id == game_id]

def get_tournaments_by_game(game_id):
    return [tournament for tournament in TOURNAMENTS if tournament.game_id == game_id]

def get_players_by_game(game_id):
    return [player for player in PLAYERS if player.game_id == game_id]

def get_tournament_by_id(tournament_id):
    return next((tournament for tournament in TOURNAMENTS if tournament.id == tournament_id), None)

# Pro Player Settings Data
PRO_PLAYER_SETTINGS = [
    # CS2 Pro Settings
    ProPlayerSettings(1, 1, 1,
        mouse_settings={
            "dpi": 400,
            "sensitivity": 2.0,
            "zoom_sensitivity": 1.0,
            "raw_input": True,
            "mouse_acceleration": False,
            "polling_rate": 1000
        },
        video_settings={
            "resolution": "1920x1080",
            "aspect_ratio": "16:9",
            "display_mode": "Fullscreen",
            "brightness": 1.6,
            "digital_vibrance": 75,
            "multisampling": "None",
            "texture_filtering": "Bilinear",
            "shader_detail": "Low",
            "effect_detail": "Low",
            "global_shadow": "Very Low",
            "model_detail": "High"
        },
        audio_settings={
            "master_volume": 0.5,
            "headphone_mode": True,
            "voice_enable": True,
            "voice_scale": 0.4,
            "windows_speaker_config": 1
        },
        crosshair_settings={
            "style": 4,
            "size": 2,
            "thickness": 0,
            "gap": -3,
            "outline": True,
            "outline_thickness": 1,
            "color": "Green",
            "alpha": 255,
            "dot": False
        },
        equipment={
            "mouse": "Logitech G Pro X Superlight",
            "mouse_image": "/static/images/mice/logitech_gpro_superlight.jpg",
            "mousepad": "SteelSeries QcK+",
            "keyboard": "Logitech G Pro X",
            "headset": "HyperX Cloud II",
            "monitor": "ASUS ROG Swift PG240Q"
        }
    ),

    ProPlayerSettings(2, 2, 1,
        mouse_settings={
            "dpi": 800,
            "sensitivity": 1.3,
            "zoom_sensitivity": 1.0,
            "raw_input": True,
            "mouse_acceleration": False,
            "polling_rate": 1000
        },
        video_settings={
            "resolution": "1920x1080",
            "aspect_ratio": "16:9",
            "display_mode": "Fullscreen",
            "brightness": 1.8,
            "digital_vibrance": 100,
            "multisampling": "None",
            "texture_filtering": "Bilinear",
            "shader_detail": "Low",
            "effect_detail": "Low",
            "global_shadow": "Very Low",
            "model_detail": "Medium"
        },
        audio_settings={
            "master_volume": 0.6,
            "headphone_mode": True,
            "voice_enable": True,
            "voice_scale": 0.5,
            "windows_speaker_config": 1
        },
        crosshair_settings={
            "style": 4,
            "size": 1,
            "thickness": 1,
            "gap": -2,
            "outline": True,
            "outline_thickness": 1,
            "color": "Cyan",
            "alpha": 200,
            "dot": False
        },
        equipment={
            "mouse": "Razer DeathAdder V3 Pro",
            "mouse_image": "/static/images/mice/razer_deathadder_v3_pro.jpg",
            "mousepad": "Razer Gigantus V2",
            "keyboard": "Razer Huntsman V2",
            "headset": "SteelSeries Arctis Pro",
            "monitor": "BenQ ZOWIE XL2546K"
        }
    )
]

# Community Settings Data
COMMUNITY_SETTINGS = [
    CommunitySettings(1, "Shroud's Competitive Config", "Shroud", "streamer",
                     "Perfect settings for competitive play with optimal visibility", 1,
                     {
                         "mouse_dpi": 450,
                         "sensitivity": 2.5,
                         "crosshair": "CSGO-YaQaK-jJhOJ-GPBQP-fqJAJ-6BXQQ",
                         "resolution": "1920x1080"
                     }, 15420, 4.8),

    CommunitySettings(2, "TenZ Valorant Settings", "TenZ", "content_creator",
                     "High-performance settings used by professional Valorant player", 2,
                     {
                         "mouse_dpi": 800,
                         "sensitivity": 0.347,
                         "crosshair": "0;P;c;5;o;1;d;1;z;3;f;0;s;0;0l;4;0o;2;0a;1;0f;0;1b;0",
                         "resolution": "1920x1080"
                     }, 8930, 4.9),

    CommunitySettings(3, "Reika's Competitive Setting", "Reika_Gaming", "user",
                     "My personal competitive settings optimized for ranked gameplay", 1,
                     {
                         "mouse_dpi": 400,
                         "sensitivity": 1.8,
                         "crosshair": "CSGO-3mJAz-LkqSF-GPBQP-fqJAJ-6BXQQ",
                         "resolution": "1920x1080"
                     }, 2340, 4.2)
]

def get_player_by_id(player_id):
    return next((player for player in PLAYERS if player.id == player_id), None)

def get_pro_settings_by_player(player_id):
    return next((settings for settings in PRO_PLAYER_SETTINGS if settings.player_id == player_id), None)

def get_pro_settings_by_game(game_id):
    return [settings for settings in PRO_PLAYER_SETTINGS if settings.game_id == game_id]

def get_community_settings_by_game(game_id):
    return [settings for settings in COMMUNITY_SETTINGS if settings.game_id == game_id]

def get_community_settings_by_id(settings_id):
    return next((settings for settings in COMMUNITY_SETTINGS if settings.id == settings_id), None)

def get_team_by_id(team_id):
    return next((team for team in ESPORTS_TEAMS if team.id == team_id), None)

def get_match_by_id(match_id):
    return next((match for match in MATCHES if match.id == match_id), None)

def get_matches_by_team(team_id):
    return [match for match in MATCHES if match.team1_id == team_id or match.team2_id == team_id]

def get_team_players(team):
    """Get player objects for a team"""
    return [player for player in PLAYERS if player.id in team.players]

def get_team_recent_matches(team):
    """Get recent match objects for a team"""
    return [get_match_by_id(match_id) for match_id in team.recent_matches if get_match_by_id(match_id)]

def get_player_recent_matches(player):
    """Get recent match objects for a player"""
    return [get_match_by_id(match_id) for match_id in player.recent_matches if get_match_by_id(match_id)]

def get_player_current_team(player):
    """Get the current team object for a player"""
    if player.current_team_id:
        return get_team_by_id(player.current_team_id)
    return None

def get_players_by_team_id(team_id):
    """Get all players for a specific team"""
    return [player for player in PLAYERS if player.current_team_id == team_id]

# Enhanced Data Integration with API Support
def initialize_enhanced_data():
    """Initialize enhanced data models if available"""
    global ENHANCED_PLAYERS, ENHANCED_TEAMS, ENHANCED_TOURNAMENTS

    if ENHANCED_MODELS_AVAILABLE:
        try:
            generator = RealisticDataGenerator()
            ENHANCED_PLAYERS = generator.create_realistic_players()
            ENHANCED_TEAMS = generator.create_realistic_teams()
            print(f"✅ Enhanced data loaded: {len(ENHANCED_PLAYERS)} players, {len(ENHANCED_TEAMS)} teams")
            return True
        except Exception as e:
            print(f"❌ Error loading enhanced data: {e}")
            return False
    else:
        print("ℹ️ Enhanced models not available, using basic data")
    return False

def get_enhanced_player_data(player_id):
    """Get enhanced player data with comprehensive statistics"""
    if ENHANCED_MODELS_AVAILABLE and 'ENHANCED_PLAYERS' in globals():
        enhanced_player = next((p for p in ENHANCED_PLAYERS if p.id == player_id), None)
        if enhanced_player:
            return {
                'enhanced': True,
                'player': enhanced_player,
                'statistics': enhanced_player.statistics,
                'achievements': enhanced_player.achievements,
                'team_history': enhanced_player.team_history,
                'social_media': enhanced_player.social_media,
                'equipment': enhanced_player.equipment
            }
    return {'enhanced': False, 'player': get_player_by_id(player_id)}

def get_enhanced_team_data(team_id):
    """Get enhanced team data with comprehensive statistics"""
    if ENHANCED_MODELS_AVAILABLE and 'ENHANCED_TEAMS' in globals():
        enhanced_team = next((t for t in ENHANCED_TEAMS if t.id == team_id), None)
        if enhanced_team:
            return {
                'enhanced': True,
                'team': enhanced_team,
                'statistics': enhanced_team.statistics,
                'social_media': enhanced_team.social_media,
                'achievements': enhanced_team.tournament_wins
            }
    return {'enhanced': False, 'team': get_team_by_id(team_id)}

def get_realistic_player_statistics(player_id, tier="tier1"):
    """Get realistic player statistics using API patterns"""
    from api_integrations import MockDataGenerator

    mock_generator = MockDataGenerator()
    base_stats = mock_generator.generate_realistic_player_stats(tier)

    # Add additional professional metrics
    enhanced_stats = {
        **base_stats,
        'impact_rating': round(base_stats['rating_2_1'] * 1.1, 2),
        'first_kill_rate': round(random.uniform(0.12, 0.18), 3),
        'first_death_rate': round(random.uniform(0.08, 0.14), 3),
        'utility_damage_per_round': round(random.uniform(8.0, 15.0), 1),
        'flash_assists_per_round': round(random.uniform(0.05, 0.12), 3),
        'maps_played': random.randint(80, 200),
        'rounds_played': random.randint(2000, 5000)
    }

    return enhanced_stats

def simulate_api_data_fetch(player_nickname, platform="faceit"):
    """Simulate fetching data from external APIs"""
    import random
    import time

    # Simulate API delay
    time.sleep(0.1)

    if platform == "faceit":
        return {
            'platform': 'FACEIT',
            'elo': random.randint(2000, 3500),
            'level': min(10, max(1, random.randint(7, 10))),
            'matches_played': random.randint(500, 2000),
            'win_rate': round(random.uniform(55.0, 75.0), 1),
            'avg_kd': round(random.uniform(1.0, 1.4), 2),
            'avg_headshot_percentage': round(random.uniform(40.0, 65.0), 1)
        }
    elif platform == "steam":
        return {
            'platform': 'Steam',
            'hours_played': random.randint(3000, 8000),
            'achievements_unlocked': random.randint(50, 100),
            'last_online': 'Recently',
            'profile_level': random.randint(20, 150)
        }

    return {}

# Initialize enhanced data on import
try:
    import random  # Needed for realistic data generation
    ENHANCED_DATA_LOADED = initialize_enhanced_data()
    print(f"🎮 FPS Hub Enhanced Database System {'✅ Active' if ENHANCED_DATA_LOADED else '⚠️ Basic Mode'}")
except Exception as e:
    print(f"⚠️ Enhanced data initialization failed: {e}")
    ENHANCED_DATA_LOADED = False
