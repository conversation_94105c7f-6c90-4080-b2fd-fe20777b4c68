from flask import Flask, render_template, request, jsonify, send_file
from data import (GAMES, get_game_by_id, get_characters_by_game, get_maps_by_game,
                  get_news_by_game, get_settings_by_game, get_skills_by_game, get_forum_posts_by_game,
                  get_teams_by_game, get_tournaments_by_game, get_players_by_game,
                  get_tournament_by_id, get_player_by_id, get_pro_settings_by_game,
                  get_pro_settings_by_player, get_community_settings_by_game, get_community_settings_by_id,
                  get_team_by_id, get_team_players, get_team_recent_matches, get_player_recent_matches,
                  get_player_current_team, get_players_by_team_id)
import io
import os

app = Flask(__name__)

@app.route('/')
def index():
    """Homepage showing all FPS games"""
    return render_template('index.html', games=GAMES)

@app.route('/game/<int:game_id>')
def game_detail(game_id):
    """Individual game page with tabs"""
    game = get_game_by_id(game_id)
    if not game:
        return "Game not found", 404

    # Get the active tab from query parameter, default to 'info'
    active_tab = request.args.get('tab', 'info')

    return render_template('game_detail.html', game=game, active_tab=active_tab)

@app.route('/game/<int:game_id>/info')
def game_info(game_id):
    """Game information including characters and maps"""
    game = get_game_by_id(game_id)
    if not game:
        return "Game not found", 404

    characters = get_characters_by_game(game_id)
    maps = get_maps_by_game(game_id)

    return render_template('game_info.html', game=game, characters=characters, maps=maps)

@app.route('/game/<int:game_id>/news')
def esports_news(game_id):
    """E-sports news for the game with teams, tournaments, players, and news"""
    game = get_game_by_id(game_id)
    if not game:
        return "Game not found", 404

    teams = get_teams_by_game(game_id)
    tournaments = get_tournaments_by_game(game_id)
    players = get_players_by_game(game_id)
    news_articles = get_news_by_game(game_id)

    return render_template('esports_news.html',
                         game=game,
                         teams=teams,
                         tournaments=tournaments,
                         players=players,
                         news_articles=news_articles)

@app.route('/game/<int:game_id>/settings')
def game_settings(game_id):
    """Settings hub with Pro Settings and Community Settings options"""
    game = get_game_by_id(game_id)
    if not game:
        return "Game not found", 404

    return render_template('settings.html', game=game)

@app.route('/game/<int:game_id>/skills')
def game_skills(game_id):
    """Game skills and tips"""
    game = get_game_by_id(game_id)
    if not game:
        return "Game not found", 404

    skills = get_skills_by_game(game_id)

    return render_template('skills.html', game=game, skills=skills)

@app.route('/game/<int:game_id>/forum')
def game_forum(game_id):
    """Community forum for the game"""
    game = get_game_by_id(game_id)
    if not game:
        return "Game not found", 404

    forum_posts = get_forum_posts_by_game(game_id)

    return render_template('forum.html', game=game, forum_posts=forum_posts)

@app.route('/game/<int:game_id>/tournaments')
def all_tournaments(game_id):
    """All tournaments page"""
    game = get_game_by_id(game_id)
    if not game:
        return "Game not found", 404

    tournaments = get_tournaments_by_game(game_id)

    return render_template('all_tournaments.html', game=game, tournaments=tournaments)

@app.route('/tournament/<int:tournament_id>')
def tournament_detail(tournament_id):
    """Tournament detail page"""
    tournament = get_tournament_by_id(tournament_id)
    if not tournament:
        return "Tournament not found", 404

    game = get_game_by_id(tournament.game_id)

    return render_template('tournament_detail.html', tournament=tournament, game=game)

@app.route('/game/<int:game_id>/players')
def all_players(game_id):
    """All players page"""
    game = get_game_by_id(game_id)
    if not game:
        return "Game not found", 404

    players = get_players_by_game(game_id)

    return render_template('all_players.html', game=game, players=players)

@app.route('/player/<int:player_id>')
def player_detail(player_id):
    """Player detail page"""
    player = get_player_by_id(player_id)
    if not player:
        return "Player not found", 404

    game = get_game_by_id(player.game_id)
    current_team = get_player_current_team(player)
    recent_matches = get_player_recent_matches(player)
    pro_settings = get_pro_settings_by_player(player_id)

    try:
        return render_template('player_detail.html',
                             player=player,
                             game=game,
                             current_team=current_team,
                             recent_matches=recent_matches,
                             pro_settings=pro_settings)
    except Exception as e:
        return f"""
        <h1>Template Error</h1>
        <p>Error rendering player profile: {str(e)}</p>
        <p><a href="/debug-player/{player_id}">Debug Player Data</a></p>
        <p><a href="/test-players">← Back to Test Page</a></p>
        """

@app.route('/game/<int:game_id>/all-news')
def all_news(game_id):
    """All news page"""
    game = get_game_by_id(game_id)
    if not game:
        return "Game not found", 404

    news_articles = get_news_by_game(game_id)

    return render_template('all_news.html', game=game, news_articles=news_articles)

@app.route('/team/<int:team_id>')
def team_detail(team_id):
    """Team detail page"""
    team = get_team_by_id(team_id)
    if not team:
        return "Team not found", 404

    game = get_game_by_id(team.game_id)
    team_players = get_team_players(team)
    recent_matches = get_team_recent_matches(team)

    return render_template('team_detail.html',
                         team=team,
                         game=game,
                         team_players=team_players,
                         recent_matches=recent_matches)

@app.route('/game/<int:game_id>/teams')
def all_teams(game_id):
    """All teams page"""
    game = get_game_by_id(game_id)
    if not game:
        return "Game not found", 404

    teams = get_teams_by_game(game_id)

    return render_template('all_teams.html', game=game, teams=teams)

@app.route('/test-players')
def test_players():
    """Test page to quickly access player profiles"""
    from data import PLAYERS, ENHANCED_DATA_LOADED

    enhanced_status = "✅ Active" if ENHANCED_DATA_LOADED else "⚠️ Basic Mode"

    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>FPS Hub - Player Profile Testing</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            .enhanced-badge {{ background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; }}
            .status-badge {{ background: linear-gradient(45deg, #667eea, #764ba2); color: white; }}
        </style>
    </head>
    <body>
        <div class="container py-4">
            <div class="row mb-4">
                <div class="col-12">
                    <h1>🎮 FPS Hub Player Profile Testing</h1>
                    <span class="badge status-badge fs-6">Enhanced Database System: {enhanced_status}</span>
                    <p class="mt-2">Test different player profile implementations and API integrations</p>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Player</th>
                            <th>HLTV-Style Profile</th>
                            <th>Enhanced API Profile</th>
                            <th>Simple Profile</th>
                            <th>Debug Data</th>
                        </tr>
                    </thead>
                    <tbody>
                        {''.join([f'''
                        <tr>
                            <td>
                                <strong>{player.name}</strong><br>
                                <small class="text-muted">{player.real_name} • {player.team}</small>
                            </td>
                            <td><a href="/player/{player.id}" class="btn btn-primary btn-sm">HLTV Style</a></td>
                            <td>
                                <a href="/enhanced-player/{player.id}" class="btn btn-success btn-sm">
                                    🚀 Enhanced API
                                </a>
                            </td>
                            <td><a href="/player-simple/{player.id}" class="btn btn-outline-secondary btn-sm">Simple</a></td>
                            <td><a href="/debug-player/{player.id}" class="btn btn-outline-info btn-sm">Debug</a></td>
                        </tr>
                        ''' for player in PLAYERS])}
                    </tbody>
                </table>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6>🎯 Profile Types</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><strong>HLTV Style:</strong> Professional esports design</li>
                                <li><strong>Enhanced API:</strong> Real-time data integration</li>
                                <li><strong>Simple:</strong> Basic information display</li>
                                <li><strong>Debug:</strong> Raw data inspection</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6>🔗 API Integration Features</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li>✅ FACEIT statistics simulation</li>
                                <li>✅ Steam profile integration</li>
                                <li>✅ Professional equipment database</li>
                                <li>✅ Enhanced performance metrics</li>
                                <li>✅ Real-time data aggregation</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <a href="/" class="btn btn-outline-primary">← Back to Home</a>
                <a href="/enhanced-player/1" class="btn btn-success">🚀 Try Enhanced Profile (s1mple)</a>
            </div>
        </div>
    </body>
    </html>
    """

@app.route('/debug-player/<int:player_id>')
def debug_player(player_id):
    """Debug player data"""
    player = get_player_by_id(player_id)
    if not player:
        return "Player not found", 404

    game = get_game_by_id(player.game_id)
    current_team = get_player_current_team(player)
    recent_matches = get_player_recent_matches(player)
    pro_settings = get_pro_settings_by_player(player_id)

    return f"""
    <h1>Debug Player Data</h1>
    <h2>Player: {player.name}</h2>
    <p><strong>Real Name:</strong> {player.real_name}</p>
    <p><strong>Team:</strong> {player.team}</p>
    <p><strong>Current Team ID:</strong> {player.current_team_id}</p>
    <p><strong>Current Team Object:</strong> {current_team.name if current_team else 'None'}</p>
    <p><strong>Game:</strong> {game.name if game else 'None'}</p>
    <p><strong>Stats:</strong> {player.stats}</p>
    <p><strong>Social Links:</strong> {player.social_links}</p>
    <p><strong>Recent Matches:</strong> {len(recent_matches)} matches</p>
    <p><a href="/player/{player_id}">View Player Profile</a></p>
    <p><a href="/test-players">← Back to Test Page</a></p>
    """

@app.route('/enhanced-player/<int:player_id>')
def enhanced_player_detail(player_id):
    """Enhanced player detail page with API integration"""
    from data import get_enhanced_player_data, simulate_api_data_fetch, get_realistic_player_statistics

    # Get enhanced player data
    player_data = get_enhanced_player_data(player_id)

    if not player_data['enhanced']:
        return redirect(url_for('player_detail', player_id=player_id))

    enhanced_player = player_data['player']

    # Simulate API data fetching
    faceit_data = simulate_api_data_fetch(enhanced_player.nickname, "faceit")
    steam_data = simulate_api_data_fetch(enhanced_player.nickname, "steam")

    # Get realistic statistics
    realistic_stats = get_realistic_player_statistics(player_id, "tier1")

    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Enhanced Player Profile - {enhanced_player.nickname}</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            .api-card {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }}
            .stat-card {{ background: #f8f9fa; border-left: 4px solid #007bff; }}
            .enhanced-badge {{ background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; }}
        </style>
    </head>
    <body>
        <div class="container py-4">
            <div class="row mb-4">
                <div class="col-12">
                    <span class="badge enhanced-badge fs-6 mb-2">🚀 ENHANCED PROFILE</span>
                    <h1>{enhanced_player.nickname} <small class="text-muted">({enhanced_player.real_name})</small></h1>
                    <p class="lead">{enhanced_player.current_team_name} • {enhanced_player.country} • {enhanced_player.primary_role.value}</p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card api-card mb-3">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-line"></i> Professional Statistics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <h3>{enhanced_player.statistics.rating_2_1}</h3>
                                    <small>Rating 2.1</small>
                                </div>
                                <div class="col-6">
                                    <h3>{enhanced_player.statistics.kd_ratio}</h3>
                                    <small>K/D Ratio</small>
                                </div>
                            </div>
                            <hr>
                            <div class="row text-center">
                                <div class="col-6">
                                    <h4>{enhanced_player.statistics.adr}</h4>
                                    <small>ADR</small>
                                </div>
                                <div class="col-6">
                                    <h4>{enhanced_player.statistics.kast}%</h4>
                                    <small>KAST</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-3">
                        <div class="card-header">
                            <h6>💰 Career Information</h6>
                        </div>
                        <div class="card-body">
                            <p><strong>Prize Money:</strong> ${enhanced_player.career_prize_money:,}</p>
                            <p><strong>Maps Played:</strong> {enhanced_player.statistics.maps_played}</p>
                            <p><strong>Total Kills:</strong> {enhanced_player.statistics.total_kills:,}</p>
                            <p><strong>Role:</strong> {enhanced_player.primary_role.value}</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-8">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card stat-card mb-3">
                                <div class="card-header">
                                    <h6>🎮 FACEIT Statistics</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>ELO:</strong> {faceit_data['elo']}</p>
                                    <p><strong>Level:</strong> {faceit_data['level']}/10</p>
                                    <p><strong>Win Rate:</strong> {faceit_data['win_rate']}%</p>
                                    <p><strong>Avg K/D:</strong> {faceit_data['avg_kd']}</p>
                                    <p><strong>Headshot %:</strong> {faceit_data['avg_headshot_percentage']}%</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card stat-card mb-3">
                                <div class="card-header">
                                    <h6>⚡ Steam Profile</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Hours Played:</strong> {steam_data['hours_played']:,}</p>
                                    <p><strong>Profile Level:</strong> {steam_data['profile_level']}</p>
                                    <p><strong>Achievements:</strong> {steam_data['achievements_unlocked']}/100</p>
                                    <p><strong>Status:</strong> {steam_data['last_online']}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-3">
                        <div class="card-header">
                            <h6>🏆 Recent Achievements</h6>
                        </div>
                        <div class="card-body">
                            {''.join([f'<div class="mb-2"><strong>{achievement.tournament_name}</strong> - {achievement.placement} {"👑 MVP" if achievement.mvp_award else ""}<br><small class="text-muted">${achievement.prize_money:,} • {achievement.date.strftime("%B %Y")}</small></div>' for achievement in enhanced_player.achievements[:3]])}
                        </div>
                    </div>

                    <div class="card mb-3">
                        <div class="card-header">
                            <h6>🔧 Equipment Setup</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Mouse:</strong> {enhanced_player.equipment.mouse}</p>
                                    <p><strong>DPI:</strong> {enhanced_player.equipment.mouse_dpi}</p>
                                    <p><strong>Sensitivity:</strong> {enhanced_player.equipment.mouse_sensitivity}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Monitor:</strong> {enhanced_player.equipment.monitor}</p>
                                    <p><strong>Refresh Rate:</strong> {enhanced_player.equipment.monitor_refresh_rate}Hz</p>
                                    <p><strong>Resolution:</strong> {enhanced_player.equipment.monitor_resolution}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h6>🔗 API Integration Features:</h6>
                        <ul class="mb-0">
                            <li>Real-time FACEIT statistics integration</li>
                            <li>Steam profile data synchronization</li>
                            <li>Professional equipment database</li>
                            <li>Enhanced performance metrics</li>
                            <li>Comprehensive achievement tracking</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="mt-3">
                <a href="/player/{player_id}" class="btn btn-primary">View Standard Profile</a>
                <a href="/test-players" class="btn btn-secondary">← Back to Players</a>
                <a href="/" class="btn btn-outline-primary">Home</a>
            </div>
        </div>
    </body>
    </html>
    """

@app.route('/player-simple/<int:player_id>')
def player_detail_simple(player_id):
    """Simple player detail page for testing"""
    player = get_player_by_id(player_id)
    if not player:
        return "Player not found", 404

    game = get_game_by_id(player.game_id)
    current_team = get_player_current_team(player)
    recent_matches = get_player_recent_matches(player)
    pro_settings = get_pro_settings_by_player(player_id)

    return render_template('player_detail_simple.html',
                         player=player,
                         game=game,
                         current_team=current_team,
                         recent_matches=recent_matches,
                         pro_settings=pro_settings)

@app.route('/game/<int:game_id>/pro-settings')
def pro_settings_list(game_id):
    """List of pro players for settings"""
    game = get_game_by_id(game_id)
    if not game:
        return "Game not found", 404

    players = get_players_by_game(game_id)

    return render_template('pro_settings_list.html', game=game, players=players)

@app.route('/pro-settings/<int:player_id>')
def pro_settings_detail(player_id):
    """Detailed pro player settings"""
    player = get_player_by_id(player_id)
    if not player:
        return "Player not found", 404

    game = get_game_by_id(player.game_id)
    settings = get_pro_settings_by_player(player_id)

    return render_template('pro_settings_detail.html', player=player, game=game, settings=settings)

@app.route('/game/<int:game_id>/community-settings')
def community_settings_list(game_id):
    """Community settings list"""
    game = get_game_by_id(game_id)
    if not game:
        return "Game not found", 404

    community_settings = get_community_settings_by_game(game_id)

    return render_template('community_settings_list.html', game=game, community_settings=community_settings)

@app.route('/community-settings/<int:settings_id>')
def community_settings_detail(settings_id):
    """Detailed community settings"""
    settings = get_community_settings_by_id(settings_id)
    if not settings:
        return "Settings not found", 404

    game = get_game_by_id(settings.game_id)

    return render_template('community_settings_detail.html', settings=settings, game=game)

@app.route('/download-config/<int:player_id>')
def download_config(player_id):
    """Download pro player config file"""
    player = get_player_by_id(player_id)
    settings = get_pro_settings_by_player(player_id)

    if not player or not settings:
        return "Settings not found", 404

    # Generate config file content
    config_content = generate_config_file(settings, player)

    # Create file-like object
    config_file = io.StringIO(config_content)
    config_bytes = io.BytesIO(config_file.getvalue().encode('utf-8'))

    return send_file(
        config_bytes,
        as_attachment=True,
        download_name=f"{player.name}_config.cfg",
        mimetype='text/plain'
    )

def generate_config_file(settings, player):
    """Generate CS2 config file content"""
    config_lines = [
        f"// {player.name}'s Professional Config",
        f"// Team: {player.team}",
        "",
        "// Mouse Settings",
        f'sensitivity "{settings.mouse_settings["sensitivity"]}"',
        f'm_rawinput "{1 if settings.mouse_settings["raw_input"] else 0}"',
        f'zoom_sensitivity_ratio_mouse "{settings.mouse_settings["zoom_sensitivity"]}"',
        "",
        "// Video Settings",
        f'mat_monitorgamma "{settings.video_settings["brightness"]}"',
        f'mat_queue_mode "{-1}"',
        "",
        "// Audio Settings",
        f'volume "{settings.audio_settings["master_volume"]}"',
        f'voice_enable "{1 if settings.audio_settings["voice_enable"] else 0}"',
        f'voice_scale "{settings.audio_settings["voice_scale"]}"',
        "",
        "// Crosshair Settings",
        f'cl_crosshair_drawoutline "{1 if settings.crosshair_settings["outline"] else 0}"',
        f'cl_crosshair_outlinethickness "{settings.crosshair_settings["outline_thickness"]}"',
        f'cl_crosshairsize "{settings.crosshair_settings["size"]}"',
        f'cl_crosshairthickness "{settings.crosshair_settings["thickness"]}"',
        f'cl_crosshairgap "{settings.crosshair_settings["gap"]}"',
        f'cl_crosshairstyle "{settings.crosshair_settings["style"]}"',
        f'cl_crosshairalpha "{settings.crosshair_settings["alpha"]}"',
        f'cl_crosshairdot "{1 if settings.crosshair_settings["dot"] else 0}"',
        "",
        "echo \"Config loaded successfully!\"",
        f"echo \"Welcome to {player.name}'s settings!\""
    ]

    return "\n".join(config_lines)

@app.route('/upload-mouse-image', methods=['GET', 'POST'])
def upload_mouse_image():
    """Handle mouse image uploads specifically"""
    if request.method == 'POST':
        if 'file' not in request.files:
            return jsonify({"error": "No file part"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"error": "No selected file"}), 400

        if file and allowed_file(file.filename):
            # Get mouse model from form
            mouse_model = request.form.get('mouse_model', '')
            if not mouse_model:
                return jsonify({"error": "Mouse model is required"}), 400

            # Create mice folder if it doesn't exist
            mice_folder = os.path.join('static', 'images', 'mice')
            os.makedirs(mice_folder, exist_ok=True)

            # Format filename based on mouse model
            filename = f"{mouse_model.lower().replace(' ', '_')}.{file.filename.rsplit('.', 1)[1].lower()}"
            file_path = os.path.join(mice_folder, filename)
            file.save(file_path)

            # Return the path to the saved file
            relative_path = f"/static/images/mice/{filename}"
            return jsonify({"success": True, "file_path": relative_path})

        return jsonify({"error": "File type not allowed"}), 400

    # GET request - show upload form
    return render_template('upload_mouse_image.html')

@app.route('/image-guide')
def image_guide():
    """Show image setup guide"""
    return render_template('image_guide.html')

@app.route('/image-status')
def image_status():
    """Show current image status"""
    return render_template('image_status.html')

def allowed_file(filename):
    """Check if file extension is allowed"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/upload-image', methods=['GET', 'POST'])
def upload_image():
    """Handle general image uploads"""
    if request.method == 'POST':
        if 'file' not in request.files:
            return jsonify({"error": "No file part"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"error": "No selected file"}), 400

        if file and allowed_file(file.filename):
            # Get category from form
            category = request.form.get('category', 'misc')

            # Create category folder if it doesn't exist
            category_folder = os.path.join('static', 'images', category)
            os.makedirs(category_folder, exist_ok=True)

            # Save file with original name
            filename = file.filename
            file_path = os.path.join(category_folder, filename)
            file.save(file_path)

            # Return the path to the saved file
            relative_path = f"/static/images/{category}/{filename}"
            return jsonify({"success": True, "file_path": relative_path})

        return jsonify({"error": "File type not allowed"}), 400

    # GET request - show upload form
    return render_template('upload_image.html')

@app.route('/manage-images')
def manage_images():
    """Show image management page"""
    # Get all images organized by category
    images = {}
    mice_images = []

    base_path = os.path.join('static', 'images')
    if os.path.exists(base_path):
        for category in ['games', 'characters', 'maps', 'players', 'teams', 'tournaments', 'misc']:
            category_path = os.path.join(base_path, category)
            if os.path.exists(category_path):
                category_images = []
                for filename in os.listdir(category_path):
                    if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
                        category_images.append(f"/static/images/{category}/{filename}")
                images[category] = category_images

        # Special handling for mice images
        mice_path = os.path.join(base_path, 'mice')
        if os.path.exists(mice_path):
            for filename in os.listdir(mice_path):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
                    mice_images.append(f"/static/images/mice/{filename}")

    return render_template('manage_images.html', images=images, mice_images=mice_images)

@app.route('/image-demo')
def image_demo():
    """Show image system demonstration"""
    return render_template('image_demo.html')

@app.route('/hltv-guide')
def hltv_guide():
    """Show HLTV image download guide"""
    return render_template('hltv_guide.html')

if __name__ == '__main__':
    app.run(debug=True, port=5000)
