{% extends "base.html" %}

{% block title %}{{ player.real_name }} '{{ player.name }}' - {{ game.name }} Player Profile - FPS Hub{% endblock %}

{% block content %}
<!-- Player Hero Section -->
<div class="player-hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-3 text-center">
                {% if current_team %}
                <div class="team-logo-large mb-3">
                    <i class="fas fa-shield-alt fa-4x text-primary"></i>
                </div>
                {% endif %}
                <div class="player-image-large">
                    {% if player.photos and player.photos|length > 0 and player.photos[0].startswith('/static/') %}
                    <img src="{{ player.photos[0] }}" class="player-hero-image" alt="{{ player.name }}" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <i class="fas fa-user-circle fa-6x text-white" style="display: none;"></i>
                    {% else %}
                    <i class="fas fa-user-circle fa-6x text-white"></i>
                    {% endif %}
                </div>
            </div>
            <div class="col-lg-6">
                <div class="player-info">
                    <h1 class="player-name-hero">{{ player.name }}</h1>
                    <h2 class="player-real-name">{{ player.real_name }}</h2>

                    <!-- Social Links -->
                    <div class="social-links mb-3">
                        {% if player.social_links.twitter %}
                        <a href="{{ player.social_links.twitter }}" target="_blank" class="social-link">
                            <i class="fab fa-twitter"></i>
                        </a>
                        {% endif %}
                        {% if player.social_links.twitch %}
                        <a href="{{ player.social_links.twitch }}" target="_blank" class="social-link">
                            <i class="fab fa-twitch"></i>
                        </a>
                        {% endif %}
                        {% if player.social_links.instagram %}
                        <a href="{{ player.social_links.instagram }}" target="_blank" class="social-link">
                            <i class="fab fa-instagram"></i>
                        </a>
                        {% endif %}
                        {% if player.social_links.faceit %}
                        <a href="{{ player.social_links.faceit }}" target="_blank" class="social-link">
                            <i class="fas fa-gamepad"></i>
                        </a>
                        {% endif %}
                    </div>

                    <!-- Player Details -->
                    <div class="player-details">
                        {% if player.age %}
                        <div class="detail-item">
                            <span class="detail-label">Age</span>
                            <span class="detail-value">{{ player.age }} years</span>
                        </div>
                        {% endif %}

                        <div class="detail-item">
                            <span class="detail-label">Current team</span>
                            <span class="detail-value">
                                {% if current_team %}
                                <a href="{{ url_for('team_detail', team_id=current_team.id) }}" class="team-link">
                                    <i class="fas fa-shield-alt me-1"></i>{{ current_team.name }}
                                </a>
                                {% else %}
                                {{ player.team }}
                                {% endif %}
                            </span>
                        </div>

                        <div class="detail-item">
                            <span class="detail-label">Prize money</span>
                            <span class="detail-value text-success">{{ player.earnings }}</span>
                        </div>

                        {% if player.external_stats.top20_rankings %}
                        <div class="detail-item">
                            <span class="detail-label">Top 20</span>
                            <span class="detail-value">
                                {% for ranking in player.external_stats.top20_rankings %}
                                <span class="ranking-badge">{{ ranking }}</span>
                                {% endfor %}
                            </span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-lg-3">
                <!-- Quick Stats -->
                <div class="quick-stats-card">
                    {% if player.stats.rating_2_1 %}
                    <div class="main-rating">
                        <div class="rating-label">Rating 2.1</div>
                        <div class="rating-value">{{ "%.2f"|format(player.stats.rating_2_1) }}</div>
                        <div class="rating-indicator">
                            {% if player.stats.rating_2_1 > 1.2 %}
                            <i class="fas fa-arrow-up text-success"></i>
                            <span class="text-success">Top 10% (95th percentile)</span>
                            {% elif player.stats.rating_2_1 > 1.0 %}
                            <i class="fas fa-arrow-up text-warning"></i>
                            <span class="text-warning">Top 25% (82nd percentile)</span>
                            {% else %}
                            <i class="fas fa-arrow-down text-danger"></i>
                            <span class="text-danger">Below average</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container py-4">
    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs player-tabs" id="playerTabs">
        <li class="nav-item">
            <a class="nav-link active" data-bs-toggle="tab" href="#info">Info</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#teams">Teams</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#matches">Matches</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#achievements">Achievements</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#gallery">Gallery</a>
        </li>
        {% if player.external_stats.faceit_elo %}
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#faceit">FACEIT</a>
        </li>
        {% endif %}
    </ul>

    <!-- Tab Content -->
    <div class="tab-content mt-4">
        <!-- Info Tab -->
        <div class="tab-pane fade show active" id="info">
            <div class="row">
                <!-- Statistics Section -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">{{ player.name }} statistics (Past 3 months • {{ player.stats.maps_played or 0 }} maps)</h5>
                        </div>
                        <div class="card-body">
                            {% if player.stats %}
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="stat-category">
                                        <h6>Performance Metrics</h6>
                                        <div class="performance-stats">
                                            {% if player.stats.rating_2_1 %}
                                            <div class="performance-stat">
                                                <span class="stat-name">Rating 2.1</span>
                                                <span class="stat-value">{{ "%.2f"|format(player.stats.rating_2_1) }}</span>
                                                <div class="stat-bar">
                                                    <div class="stat-fill" style="width: {{ (player.stats.rating_2_1 * 50)|round }}%"></div>
                                                </div>
                                            </div>
                                            {% endif %}

                                            {% if player.stats.firepower %}
                                            <div class="performance-stat">
                                                <span class="stat-name">Firepower</span>
                                                <span class="stat-value">{{ player.stats.firepower }}/100</span>
                                                <div class="stat-bar">
                                                    <div class="stat-fill" style="width: {{ player.stats.firepower }}%"></div>
                                                </div>
                                            </div>
                                            {% endif %}

                                            {% if player.stats.opening %}
                                            <div class="performance-stat">
                                                <span class="stat-name">Opening</span>
                                                <span class="stat-value">{{ player.stats.opening }}/100</span>
                                                <div class="stat-bar">
                                                    <div class="stat-fill" style="width: {{ player.stats.opening }}%"></div>
                                                </div>
                                            </div>
                                            {% endif %}

                                            {% if player.stats.clutching %}
                                            <div class="performance-stat">
                                                <span class="stat-name">Clutching</span>
                                                <span class="stat-value">{{ player.stats.clutching }}/100</span>
                                                <div class="stat-bar">
                                                    <div class="stat-fill" style="width: {{ player.stats.clutching }}%"></div>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="stat-category">
                                        <h6>Detailed Stats</h6>
                                        <div class="detailed-stats">
                                            {% if player.stats.kd_ratio %}
                                            <div class="stat-row">
                                                <span class="stat-label">K/D Ratio</span>
                                                <span class="stat-number">{{ "%.2f"|format(player.stats.kd_ratio) }}</span>
                                            </div>
                                            {% endif %}

                                            {% if player.stats.adr %}
                                            <div class="stat-row">
                                                <span class="stat-label">ADR</span>
                                                <span class="stat-number">{{ "%.1f"|format(player.stats.adr) }}</span>
                                            </div>
                                            {% endif %}

                                            {% if player.stats.kast %}
                                            <div class="stat-row">
                                                <span class="stat-label">KAST</span>
                                                <span class="stat-number">{{ "%.1f"|format(player.stats.kast) }}%</span>
                                            </div>
                                            {% endif %}

                                            {% if player.stats.win_rate %}
                                            <div class="stat-row">
                                                <span class="stat-label">Win Rate</span>
                                                <span class="stat-number">{{ "%.1f"|format(player.stats.win_rate) }}%</span>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <p class="text-muted">No statistics available for this player.</p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Recent Matches -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Upcoming & recent matches</h5>
                        </div>
                        <div class="card-body">
                            {% if recent_matches %}
                                {% for match in recent_matches %}
                                <div class="match-result-item">
                                    <div class="match-teams">
                                        <span class="team-name">{{ match.team1_name }}</span>
                                        <span class="match-score">
                                            <span class="score">{{ match.team1_score }}</span>
                                            :
                                            <span class="score">{{ match.team2_score }}</span>
                                        </span>
                                        <span class="team-name">{{ match.team2_name }}</span>
                                    </div>
                                    <div class="match-info">
                                        <span class="tournament">{{ match.tournament_name }}</span>
                                        {% if match.map_name %}
                                        <span class="map">{{ match.map_name }}</span>
                                        {% endif %}
                                        <span class="date">{{ match.date.strftime('%b %d, %Y') }}</span>
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <p class="text-muted">No recent matches available.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Player Info Card -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Player Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="info-item">
                                <span class="info-label">Real Name</span>
                                <span class="info-value">{{ player.real_name }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Country</span>
                                <span class="info-value">{{ player.country }}</span>
                            </div>
                            {% if player.age %}
                            <div class="info-item">
                                <span class="info-label">Age</span>
                                <span class="info-value">{{ player.age }} years</span>
                            </div>
                            {% endif %}
                            <div class="info-item">
                                <span class="info-label">World Rank</span>
                                <span class="info-value">#{{ player.rank }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Prize Money</span>
                                <span class="info-value text-success">{{ player.earnings }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Biography -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Biography</h6>
                        </div>
                        <div class="card-body">
                            <p>{{ player.bio or "Professional " + game.name + " player competing at the highest level of competitive play." }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Teams Tab -->
        <div class="tab-pane fade" id="teams">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Team stats for {{ player.name }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="team-stats-summary">
                                <div class="stat-item">
                                    <span class="stat-label">Teams</span>
                                    <span class="stat-value">{{ player.team_history|length }}</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Days in current team</span>
                                    <span class="stat-value">{{ 24 if player.team_history else 0 }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Breakdown of {{ player.name }}'s teams</h5>
                        </div>
                        <div class="card-body">
                            {% if player.team_history %}
                            <div class="team-history-list">
                                {% for team_entry in player.team_history %}
                                <div class="team-history-item">
                                    <div class="team-period">{{ team_entry.period }}</div>
                                    <div class="team-name">
                                        {% if team_entry.team_id %}
                                        <a href="{{ url_for('team_detail', team_id=team_entry.team_id) }}">
                                            <i class="fas fa-shield-alt me-2"></i>{{ team_entry.team }}
                                        </a>
                                        {% else %}
                                        <i class="fas fa-shield-alt me-2"></i>{{ team_entry.team }}
                                        {% endif %}
                                    </div>
                                    <div class="team-achievements">
                                        {% for achievement in team_entry.achievements %}
                                        <span class="achievement-badge">{{ achievement }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <p class="text-muted">No team history available.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Timeline of {{ player.name }}'s teams</h6>
                        </div>
                        <div class="card-body">
                            <div class="team-timeline">
                                {% for team_entry in player.team_history %}
                                <div class="timeline-item">
                                    <div class="timeline-year">{{ team_entry.period.split(' - ')[0].split(' ')[-1] }}</div>
                                    <div class="timeline-team">{{ team_entry.team }}</div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Matches Tab -->
        <div class="tab-pane fade" id="matches">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Latest results for {{ player.name }}</h5>
                </div>
                <div class="card-body">
                    {% if recent_matches %}
                    <div class="matches-list">
                        {% for match in recent_matches %}
                        <div class="match-detail-item">
                            <div class="match-date">{{ match.date.strftime('%d/%m/%Y') }}</div>
                            <div class="match-teams-detail">
                                <span class="team-name">{{ match.team1_name }}</span>
                                <span class="match-score-detail">
                                    <span class="score">{{ match.team1_score }}</span>
                                    :
                                    <span class="score">{{ match.team2_score }}</span>
                                </span>
                                <span class="team-name">{{ match.team2_name }}</span>
                            </div>
                            <div class="match-tournament">{{ match.tournament_name }}</div>
                            {% if match.map_name %}
                            <div class="match-map">{{ match.map_name }}</div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">No recent matches available.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Achievements Tab -->
        <div class="tab-pane fade" id="achievements">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Major achievements for {{ player.name }}</h5>
                </div>
                <div class="card-body">
                    {% if player.major_achievements %}
                    <div class="achievements-grid">
                        {% for achievement in player.major_achievements %}
                        <div class="achievement-card">
                            <div class="achievement-placement">
                                {% if achievement.placement == "1st" %}
                                <i class="fas fa-trophy fa-2x text-warning"></i>
                                {% elif achievement.placement == "2nd" %}
                                <i class="fas fa-medal fa-2x text-secondary"></i>
                                {% else %}
                                <i class="fas fa-award fa-2x text-bronze"></i>
                                {% endif %}
                                <span class="placement-text">{{ achievement.placement }}</span>
                            </div>
                            <div class="achievement-info">
                                <h6 class="tournament-name">{{ achievement.tournament }}</h6>
                                {% if achievement.mvp %}
                                <span class="mvp-badge">MVP</span>
                                {% endif %}
                                <div class="prize-money">{{ achievement.prize }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">No major achievements recorded.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Gallery Tab -->
        <div class="tab-pane fade" id="gallery">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Pictures of {{ player.name }}</h5>
                </div>
                <div class="card-body">
                    {% if player.photos %}
                    <div class="row row-cols-1 row-cols-md-3 g-4">
                        {% for photo in player.photos %}
                        <div class="col">
                            <div class="card h-100">
                                {% if photo.startswith('/') %}
                                <img src="{{ photo }}" class="card-img-top" alt="{{ player.name }} photo">
                                {% else %}
                                <div class="photo-placeholder text-center p-5 bg-light">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                    <p class="text-muted mt-2">Photo placeholder</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">No photos available.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- FACEIT Tab -->
        {% if player.external_stats.faceit_elo %}
        <div class="tab-pane fade" id="faceit">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">FACEIT Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="faceit-stats">
                        <div class="faceit-main-stats">
                            <div class="faceit-stat">
                                <span class="stat-label">ELO</span>
                                <span class="stat-value">{{ player.external_stats.faceit_elo }}</span>
                            </div>
                            <div class="faceit-stat">
                                <span class="stat-label">Level</span>
                                <span class="stat-value">{{ player.external_stats.faceit_level }}</span>
                            </div>
                        </div>
                        <div class="faceit-link">
                            {% if player.social_links.faceit %}
                            <a href="{{ player.social_links.faceit }}" target="_blank" class="btn btn-primary">
                                <i class="fas fa-external-link-alt me-2"></i>View FACEIT Profile
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>



<style>
/* HLTV-Style Player Profile CSS */

/* Hero Section */
.player-hero-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 0;
}

.player-name-hero {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.player-real-name {
    font-size: 1.5rem;
    color: rgba(255,255,255,0.8);
    margin-bottom: 1rem;
}

.player-image-large {
    margin-bottom: 1rem;
}

.player-hero-image {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255,255,255,0.3);
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.1);
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: rgba(255,255,255,0.2);
    color: white;
    transform: translateY(-2px);
}

.player-details {
    margin-top: 1.5rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: rgba(255,255,255,0.7);
}

.detail-value {
    font-weight: 600;
}

.team-link {
    color: #3498db;
    text-decoration: none;
}

.team-link:hover {
    color: #2980b9;
}

.ranking-badge {
    background: rgba(255,255,255,0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

.quick-stats-card {
    background: rgba(255,255,255,0.1);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
}

.main-rating {
    margin-bottom: 1rem;
}

.rating-label {
    font-size: 0.9rem;
    color: rgba(255,255,255,0.7);
    margin-bottom: 0.5rem;
}

.rating-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.rating-indicator {
    font-size: 0.8rem;
}

/* Tabs */
.player-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 2rem;
}

.player-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 1rem 1.5rem;
    border-bottom: 3px solid transparent;
}

.player-tabs .nav-link.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: none;
}

.player-tabs .nav-link:hover {
    color: #007bff;
    border-color: transparent;
}

/* Statistics */
.performance-stats {
    margin-top: 1rem;
}

.performance-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.stat-name {
    font-weight: 500;
    color: #6c757d;
    flex: 1;
}

.stat-value {
    font-weight: 600;
    margin-left: 1rem;
    min-width: 60px;
    text-align: right;
}

.stat-bar {
    flex: 2;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    margin-left: 1rem;
    overflow: hidden;
}

.stat-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.detailed-stats {
    margin-top: 1rem;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-label {
    font-weight: 500;
    color: #6c757d;
}

.stat-number {
    font-weight: 600;
    font-size: 1.1rem;
    color: #007bff;
}

/* Matches */
.match-result-item {
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 1rem;
    background: #f8f9fa;
}

.match-teams {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
}

.team-name {
    font-weight: 600;
    min-width: 120px;
    text-align: center;
}

.match-score {
    margin: 0 1rem;
    font-size: 1.2rem;
    font-weight: bold;
}

.match-info {
    display: flex;
    justify-content: center;
    gap: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.match-detail-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 1rem;
    background: white;
}

.match-teams-detail {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.match-score-detail {
    font-weight: bold;
    color: #007bff;
}

/* Info Items */
.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    color: #6c757d;
}

.info-value {
    font-weight: 600;
}

/* Team History */
.team-stats-summary {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
}

.team-history-item {
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 1rem;
    background: white;
}

.team-period {
    font-weight: 600;
    color: #007bff;
    margin-bottom: 0.5rem;
}

.team-name {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.team-name a {
    color: #007bff;
    text-decoration: none;
}

.team-name a:hover {
    text-decoration: underline;
}

.achievement-badge {
    display: inline-block;
    background: #e9ecef;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
}

.team-timeline {
    position: relative;
}

.timeline-item {
    padding: 0.5rem 0;
    border-left: 2px solid #007bff;
    padding-left: 1rem;
    margin-bottom: 1rem;
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 0.75rem;
    width: 10px;
    height: 10px;
    background: #007bff;
    border-radius: 50%;
}

.timeline-year {
    font-weight: 600;
    color: #007bff;
}

.timeline-team {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Achievements */
.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.achievement-card {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: white;
    transition: box-shadow 0.3s ease;
}

.achievement-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.achievement-placement {
    text-align: center;
    margin-right: 1.5rem;
    min-width: 80px;
}

.placement-text {
    display: block;
    font-weight: bold;
    margin-top: 0.5rem;
}

.achievement-info {
    flex: 1;
}

.tournament-name {
    margin-bottom: 0.5rem;
    color: #343a40;
}

.mvp-badge {
    background: #ffc107;
    color: #212529;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-right: 0.5rem;
}

.prize-money {
    color: #28a745;
    font-weight: 600;
    font-size: 1.1rem;
}

.text-bronze {
    color: #cd7f32;
}

/* Gallery */
.photo-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.photo-item {
    aspect-ratio: 4/3;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.photo-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
}

/* FACEIT */
.faceit-stats {
    text-align: center;
}

.faceit-main-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
}

.faceit-stat {
    text-align: center;
}

.faceit-stat .stat-label {
    display: block;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.faceit-stat .stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .player-name-hero {
        font-size: 2rem;
    }

    .player-real-name {
        font-size: 1.2rem;
    }

    .social-links {
        justify-content: center;
    }

    .team-stats-summary {
        flex-direction: column;
        gap: 1rem;
    }

    .achievements-grid {
        grid-template-columns: 1fr;
    }

    .faceit-main-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .match-teams {
        flex-direction: column;
        gap: 0.5rem;
    }

    .match-info {
        flex-direction: column;
        gap: 0.25rem;
    }
}
</style>
{% endblock %}
