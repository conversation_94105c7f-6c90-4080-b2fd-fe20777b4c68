"""
<PERSON><PERSON><PERSON> to create sample placeholder images for FPS Hub
This creates simple colored rectangles with text to demonstrate the image system
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("PIL (Pillow) not available. Install with: pip install Pillow")

import os

def create_sample_images():
    """Create sample placeholder images"""
    
    if not PIL_AVAILABLE:
        print("Cannot create images without PIL/Pillow")
        return
    
    # Ensure directories exist
    directories = [
        'static/images/games',
        'static/images/teams', 
        'static/images/players',
        'static/images/characters',
        'static/images/maps'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    # Game images (400x300)
    games = [
        ('cs2.jpg', 'Counter-Strike 2', '#FF6B35'),
        ('valorant.jpg', 'VALORANT', '#FF4655'),
        ('cod.jpg', 'Call of Duty', '#000000'),
        ('overwatch.jpg', 'Overwatch 2', '#F99E1A'),
        ('r6.jpg', 'Rainbow Six Siege', '#0F1419')
    ]
    
    for filename, text, color in games:
        create_placeholder_image(
            f'static/images/games/{filename}',
            (400, 300),
            color,
            text,
            '#FFFFFF'
        )
    
    # Team logos (200x200)
    teams = [
        ('faze.png', 'FaZe', '#E31E24'),
        ('navi.png', 'NAVI', '#FFD700'),
        ('g2.png', 'G2', '#000000'),
        ('vitality.png', 'Vitality', '#FF6600'),
        ('astralis.png', 'Astralis', '#1E3A8A')
    ]
    
    for filename, text, color in teams:
        create_placeholder_image(
            f'static/images/teams/{filename}',
            (200, 200),
            color,
            text,
            '#FFFFFF'
        )
    
    # Player photos (400x400)
    players = [
        ('s1mple_1.jpg', 's1mple', '#4A90E2'),
        ('zywoo_1.jpg', 'ZywOo', '#FF6B35'),
        ('device_1.jpg', 'device', '#1E3A8A'),
        ('niko_1.jpg', 'NiKo', '#000000')
    ]
    
    for filename, text, color in players:
        create_placeholder_image(
            f'static/images/players/{filename}',
            (400, 400),
            color,
            text,
            '#FFFFFF'
        )
    
    # Character images (300x400)
    characters = [
        ('jett.jpg', 'Jett', '#00D4FF'),
        ('sage.jpg', 'Sage', '#5EE3A6'),
        ('wraith.jpg', 'Wraith', '#9966CC'),
        ('tracer.jpg', 'Tracer', '#FF6D00')
    ]
    
    for filename, text, color in characters:
        create_placeholder_image(
            f'static/images/characters/{filename}',
            (300, 400),
            color,
            text,
            '#FFFFFF'
        )
    
    # Map images (800x450)
    maps = [
        ('dust2.jpg', 'Dust2', '#D2B48C'),
        ('mirage.jpg', 'Mirage', '#8B4513'),
        ('bind.jpg', 'Bind', '#FF4655')
    ]
    
    for filename, text, color in maps:
        create_placeholder_image(
            f'static/images/maps/{filename}',
            (800, 450),
            color,
            text,
            '#FFFFFF'
        )
    
    print("✅ Sample images created successfully!")
    print("🎮 Your website now has placeholder images to demonstrate the system")
    print("🔄 Refresh your website to see the images!")

def create_placeholder_image(filepath, size, bg_color, text, text_color):
    """Create a simple placeholder image with text"""
    
    # Create image
    img = Image.new('RGB', size, bg_color)
    draw = ImageDraw.Draw(img)
    
    # Try to use a font, fall back to default if not available
    try:
        font_size = min(size) // 8
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        try:
            font_size = min(size) // 8
            font = ImageFont.load_default()
        except:
            font = None
    
    # Calculate text position (center)
    if font:
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
    else:
        text_width = len(text) * 10
        text_height = 20
    
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    # Draw text
    draw.text((x, y), text, fill=text_color, font=font)
    
    # Add border
    draw.rectangle([0, 0, size[0]-1, size[1]-1], outline='#CCCCCC', width=2)
    
    # Save image
    img.save(filepath)
    print(f"Created: {filepath}")

if __name__ == "__main__":
    create_sample_images()
